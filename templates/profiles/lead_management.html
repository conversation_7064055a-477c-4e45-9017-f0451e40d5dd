{% extends "bootstrap/base.html" %}
{% load static %}
{% block content %}
<div class="container mt-4">
    {% include "bootstrap/navi.html" %}

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h1>Lead Management</h1>
            <!-- Add New Lead button -->
            <button type="button"
                    class="btn btn-primary"
                    data-bs-toggle="modal"
                    data-bs-target="#addLeadModal">
                Add New Lead
            </button>
        </div>

        <div class="card-body">
            {% if leads %}
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>First Name</th>
                            <th>Last Name</th>
                            <th>Email</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for lead in leads %}
                        <tr>
                            <td>{{ lead.firstName }}</td>
                            <td>{{ lead.lastName }}</td>
                            <td>{{ lead.email }}</td>
                            <td>
                                <a href="{% url 'edit_lead' lead.id %}"
                                   class="btn btn-sm btn-primary">Edit</a>
                                <a href="{% url 'delete_lead' lead.id %}"
                                   class="btn btn-sm btn-danger"
                                   onclick="return confirm('Delete Lead?');">
                                   Delete
                                </a>
                                <a href="{% url 'process_signup' lead.id %}"
                                   class="btn btn-sm btn-success">Process Signup</a>
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p>No pending leads.</p>
            {% endif %}
        </div>
    </div>
</div>

<!-- Add New Lead Modal -->
<div class="modal fade" id="addLeadModal" tabindex="-1" aria-labelledby="addLeadModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addLeadModalLabel">Add New Lead</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form method="post" action="{% url 'add_lead' %}">
        {% csrf_token %}
        <div class="modal-body">
          {% include "profiles/add_lead.html" %}
        </div>
        <div class="modal-footer">
          <button type="button"
                  class="btn btn-secondary"
                  data-bs-dismiss="modal">
            Cancel
          </button>
          <button type="submit" class="btn btn-primary">Add</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-geocoding functionality
    const street1Input = document.getElementById('id_street1');
    const cityInput = document.getElementById('id_city');
    const latInput = document.getElementById('id_lat');
    const lngInput = document.getElementById('id_lng');

    let geocodeTimeout;

    function attemptGeocode() {
        const street1 = street1Input.value.trim();
        const city = cityInput.value.trim();

        // Only geocode if we have both street and city
        if (street1 && city) {
            const fullAddress = `${street1}, ${city}`;

            // Clear any existing timeout
            clearTimeout(geocodeTimeout);

            // Set a timeout to avoid too many API calls while typing
            geocodeTimeout = setTimeout(() => {
                geocodeAddress(fullAddress);
            }, 1000); // Wait 1 second after user stops typing
        }
    }

    function geocodeAddress(address) {
        // Show loading state
        latInput.placeholder = 'Loading...';
        lngInput.placeholder = 'Loading...';
        latInput.style.backgroundColor = '#f8f9fa';
        lngInput.style.backgroundColor = '#f8f9fa';

        fetch(`{% url 'geocode_address' %}?address=${encodeURIComponent(address)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    latInput.value = data.lat.toFixed(8);
                    lngInput.value = data.lng.toFixed(8);
                    latInput.placeholder = 'Auto-filled from address';
                    lngInput.placeholder = 'Auto-filled from address';
                    latInput.style.backgroundColor = '#d4edda'; // Light green
                    lngInput.style.backgroundColor = '#d4edda';

                    // Show success feedback
                    console.log('Address geocoded successfully:', data.formatted_address);
                    showGeocodeStatus('success', `Coordinates found for: ${data.formatted_address}`);
                } else {
                    console.error('Geocoding failed:', data.error);
                    latInput.placeholder = 'Could not geocode address';
                    lngInput.placeholder = 'Could not geocode address';
                    latInput.style.backgroundColor = '#f8d7da'; // Light red
                    lngInput.style.backgroundColor = '#f8d7da';
                    showGeocodeStatus('error', 'Could not find coordinates for this address');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                latInput.placeholder = 'Error geocoding';
                lngInput.placeholder = 'Error geocoding';
                latInput.style.backgroundColor = '#f8d7da';
                lngInput.style.backgroundColor = '#f8d7da';
                showGeocodeStatus('error', 'Error connecting to geocoding service');
            });
    }

    function showGeocodeStatus(type, message) {
        // Remove any existing status messages
        const existingStatus = document.getElementById('geocode-status');
        if (existingStatus) {
            existingStatus.remove();
        }

        // Create new status message
        const statusDiv = document.createElement('div');
        statusDiv.id = 'geocode-status';
        statusDiv.className = `alert alert-${type === 'success' ? 'success' : 'warning'} alert-dismissible fade show mt-2`;
        statusDiv.innerHTML = `
            <small><i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}</small>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Insert after the coordinates row
        const coordsRow = document.querySelector('#addLeadModal .row');
        coordsRow.parentNode.insertBefore(statusDiv, coordsRow.nextSibling);

        // Auto-remove success messages after 3 seconds
        if (type === 'success') {
            setTimeout(() => {
                if (statusDiv && statusDiv.parentNode) {
                    statusDiv.remove();
                }
            }, 3000);
        }
    }

    // Add event listeners to address fields
    street1Input.addEventListener('input', attemptGeocode);
    cityInput.addEventListener('input', attemptGeocode);

    // Manual geocode button
    const manualGeocodeBtn = document.getElementById('manual-geocode-btn');
    manualGeocodeBtn.addEventListener('click', function() {
        const street1 = street1Input.value.trim();
        const city = cityInput.value.trim();

        if (street1 && city) {
            const fullAddress = `${street1}, ${city}`;
            geocodeAddress(fullAddress);
        } else {
            showGeocodeStatus('error', 'Please enter both street address and city before geocoding');
        }
    });

    // Also try geocoding when the modal is opened if fields are already filled
    document.getElementById('addLeadModal').addEventListener('shown.bs.modal', function() {
        // Reset field styles when modal opens
        latInput.style.backgroundColor = '';
        lngInput.style.backgroundColor = '';

        // Clear any existing status messages
        const existingStatus = document.getElementById('geocode-status');
        if (existingStatus) {
            existingStatus.remove();
        }

        attemptGeocode();
    });
});
</script>

{% endblock %}
