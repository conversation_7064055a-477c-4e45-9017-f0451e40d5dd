<!-- templates/fiberPlan/signup.html -->

{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>ESVC Fiber Sign-Up</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="stylesheet" href="{% static 'css/main.css' %}">
    <style>
        body { font-family: Arial, sans-serif; background: #f8fafc; margin: 0; }
        .signup-container { max-width: 640px; margin: 2rem auto; background: #fff; padding: 2rem 2.5rem; border-radius: 12px; box-shadow: 0 2px 16px #0002;}
        .section-title { font-size: 1.2rem; margin-bottom: 0.5rem; font-weight: bold;}
        .form-field { margin-bottom: 1.3em;}
        label { display: block; margin-bottom: 0.4em; font-weight: 500;}
        input, select, textarea { width: 100%; padding: 0.6em; border: 1px solid #bbb; border-radius: 6px; }
        .plan-options { margin-bottom: 1.2em;}
        .legal { font-size: 0.95em; background: #f1f5f9; padding: 1em; border-radius: 8px; margin-bottom: 1em;}
        .signature-block { margin-top: 2em;}
        .submit-btn { width: 100%; background: #134e4a; color: #fff; padding: 0.85em; font-size: 1.15em; border-radius: 7px; border: none; font-weight: bold; cursor: pointer;}
        .submit-btn:disabled { background: #ccc;}
        .logo { width: 140px; margin-bottom: 1rem;}
    </style>
</head>
<body>
    <div class="signup-container">
        <img src="{% static 'img/esvc_logo.png' %}" alt="ESVC Logo" class="logo">
        <h2>Fiber Internet Sign-Up Form</h2>
        <p>Thank you for choosing Eastern Shore Communications. Please complete your information below to finalize your service agreement.</p>
        <form method="post" autocomplete="off">
            {% csrf_token %}
            <input type="hidden" name="token" value="{{ token }}">
            <div class="section-title">Contact Information</div>
            <div class="form-field">
                <label>Name</label>
                <input type="text" value="{{ customer.firstName }} {{ customer.lastName }}" disabled>
            </div>
            <div class="form-field">
                <label>Service Address</label>
                <input type="text" value="{{ customer.street1 }}, {{ customer.city }}" disabled>
            </div>
            <div class="form-field">
                <label>Email</label>
                <input type="email" value="{{ customer.email }}" disabled>
            </div>
            <div class="form-field">
                <label>Phone Number</label>
                <!-- Optionally add a phone number field here if stored/available -->
            </div>
            <div class="section-title">Select Your Plan</div>
            <div class="form-field plan-options">
                <select name="plan_selected" required>
                    <option value="">-- Choose Plan --</option>
                    <option value="Bronze">Bronze: $25/mo (25 Mbps/10 Mbps)</option>
                    <option value="Silver">Silver: $60/mo (250 Mbps/10 Mbps) - Most Popular</option>
                    <option value="Gold">Gold: $90/mo (500 Mbps/20 Mbps)</option>
                </select>
            </div>
            <div class="form-field">
                <label>Installation Fee</label>
                <input name="install_fee" type="number" step="0.01" placeholder="89.99">
            </div>
            <div class="form-field">
                <label>Network Maintenance Fee</label>
                <input name="network_maintenance_fee" type="number" step="0.01" placeholder="7.95">
            </div>
            <div class="form-field">
                <label>Modem Lease Fee</label>
                <input name="modem_lease_fee" type="number" step="0.01" placeholder="4.95">
            </div>
            <div class="form-field">
                <label>Router Lease Fee (if applicable)</label>
                <input name="router_lease_fee" type="number" step="0.01" placeholder="15.00">
            </div>
            <div class="form-field">
                <label>Additional Construction Costs (if any)</label>
                <input name="additional_construction_costs" type="number" step="0.01">
            </div>
            <div class="form-field">
                <label>Preferred Installation Date</label>
                <input name="preferred_installation_date" type="date">
            </div>
            <div class="legal">
                <strong>Terms & Conditions:</strong>
                <ul>
                    <li>By signing, you agree to the <a href="https://www.esvc.us/terms" target="_blank">Terms and Conditions</a> and Acceptable Internet Use Policy of Eastern Shore Communications, LLC.</li>
                    <li>One-year service agreement. Early termination fees may apply.</li>
                    <li>Customer Premise Equipment must be returned upon cancellation.</li>
                    <li>Service subject to availability and pre-installation engineering.</li>
                    <li>See <a href="https://www.esvc.us" target="_blank">www.esvc.us</a> for further details.</li>
                </ul>
            </div>
            <div class="form-field">
                <input type="checkbox" name="agrees_to_terms" required>
                <label for="agrees_to_terms">I agree to the Terms & Conditions and Acceptable Use Policy.</label>
            </div>
            <div class="form-field signature-block">
                <label for="signature">E-signature (type your full name)</label>
                <input type="text" name="signature" required>
            </div>
            <button type="submit" class="submit-btn">Submit & Sign</button>
        </form>
    </div>
</body>
</html>
