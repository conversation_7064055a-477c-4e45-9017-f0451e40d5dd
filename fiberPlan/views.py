import sys
from django.core.files.storage import FileSystemStorage
from auditlog.signals import audit_log_signal
from django.http import JsonResponse, HttpResponse, HttpResponseForbidden, Http404
from django.shortcuts import render, redirect, get_object_or_404
from django.views.decorators.csrf import csrf_protect
from django.views.generic import ListView
from django.conf import settings
from django.http import JsonResponse
from django.contrib import messages
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User, Permission
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from django.core.mail import send_mail
from django.db import models
from .models import Vault, CostQuest, Availability, Customer, FiberSignupForm, SignupToken
import os
from dotenv import load_dotenv, dotenv_values
import googlemaps
import json
import csv
from fiberPlan.myCostQuest import myCostQuest
from datetime import datetime, timedelta
import logging
from .forms import UploadFileForm
import kml2geojson
import csv
import xml.etree.ElementTree as ET



# Determine if we're running in development environment
IS_DEVELOPMENT = sys.platform.startswith('win') or 'DEVELOPMENT' in os.environ

# Set up logging directory
if IS_DEVELOPMENT:
    LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
    os.makedirs(LOG_DIR, exist_ok=True)
else:
    LOG_DIR = '/var/log/hotspot'

# Configure logging
logging.basicConfig(
    filename=os.path.join(LOG_DIR, 'auth.log'),
    filemode='a',
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def flatten_json(data):
    flat_data = {}
    for key, value in data.items():
        # Check if the value is a dictionary or a list (i.e., nested JSON)
        if not isinstance(value, (dict, list)):
            flat_data[key] = value
    return flat_data

@login_required(login_url='fiberPlan:login')
def management(request):
    context = {}
    return render(request, 'fiberplan/management.html', context)

# possible file uploads: vaults, costquest_active, costquest_corrected
@login_required(login_url='fiberPlan:login')
def upload_file(request):
    if request.method == 'POST':
        form = UploadFileForm(request.POST, request.FILES)
        if form.is_valid():
            handle_uploaded_file(request.FILES['file'], request.POST.get('dropdown'))
            filename = request.FILES['file'].name
            messages.success(request, 'File ' + filename + ' uploaded')
            return redirect('fiberPlan:management')
        else:
            print(form.errors)
    else:
        form = UploadFileForm()
    return render(request, 'fiberplan/forms/upload_file.html',{"form":form})


def kml_to_csv(kml_file, csv_file):
    # Parse the KML file
    tree = ET.parse(kml_file)
    root = tree.getroot()

    # Define the namespace (KML files often use this namespace)
    namespace = {'kml': 'http://www.opengis.net/kml/2.2'}

    # Open the CSV file for writing
    with open(csv_file, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        # Write the header row
        writer.writerow(['Name', 'Latitude', 'Longitude','Description'])

        # Extract Placemark data
        for placemark in root.findall('.//kml:Placemark', namespace):
            name = placemark.find('kml:name', namespace)
            lat = placemark.find('.//kml:latitude', namespace)
            lng = placemark.find('.//kml:longitude', namespace)
            description = placemark.find('kml:description', namespace)
            # Write data to CSV
            writer.writerow([
                name.text if name is not None else '',
                lat.text.strip() if lat is not None else '',
                lng.text.strip() if lng is not None else '',
                description.text if description is not None else '',
            ])

def kml_to_list(kml_file):
    tree = ET.parse(kml_file)
    root = tree.getroot()
    result = []
    # Define the namespace (KML files often use this namespace)
    namespace = {'kml': 'http://www.opengis.net/kml/2.2'}

    # Extract Placemark data
    for placemark in root.findall('.//kml:Placemark', namespace):
        name = placemark.find('kml:name', namespace)
        lat = placemark.find('.//kml:latitude', namespace)
        lng = placemark.find('.//kml:longitude', namespace)
        description = placemark.find('kml:description', namespace)
        result.append([
            name.text if name is not None else '',
            lat.text.strip() if lat is not None else '',
            lng.text.strip() if lng is not None else '',
            description.text if description is not None else '',
            ])
    return result

#handle file upload and add to database
#file format: lat,lng,name,is_mini,is_enc,notes,address
#file options: 1=vaults, 2=costquest_active, 3=costquest_corrected, 4=kml_to_geojson
def handle_uploaded_file(f,selected_option):
    match selected_option:
        case '1':
            # New Vaults
            i = 0
            #newVaults = kml_to_list(f)
            #Vault.objects.all().delete()
            text_content = f.read().decode('utf-8-sig').splitlines()
            reader = csv.reader(text_content, delimiter=',')
            for row in reader:
                if i>0:
                    lat = row[1]
                    lng = row[0]
                    print("working on row: " + row[0] + " " + row[1] + " " + row[2])
                    if Vault.objects.filter(lat=lat, lng=lng).exists():
                        print("Found duplicate: " + lat + " " + lng + " " + row[2])
                    else:
                        address = geocodeLatLng(lat,lng)
                        formated_address = address[0]['formatted_address']
                        name = row[2]
                        notes = row[6]
                        is_enc = row[5]
                        is_mini = row[4]
                        if not is_enc:
                            enc = 0
                        else:
                            enc = 1
                        if not is_mini:
                            mini = 0
                        else:
                            mini = 1
                        data = (name, notes, enc, mini, formated_address, lat, lng)
                        new_vault = Vault.objects.create(
                            name=data[0],
                            notes=data[1],
                            lat=data[5],
                            lng=data[6],
                            address=data[4],
                            is_enc=data[2],
                            is_mini=data[3],
                        )
                        new_vault.save()
                i+=1
        case '2':
            # FCC BDC Data
            print('FCC Active BCD')
        case '3':
            # FCC Corrections
            print('FCC corrections')
        case '4':
            print('KML to GEOJSON')
            logs_dir = 'logs'
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
            # First save the uploaded file temporarily
            fs = FileSystemStorage()
            temp_file_name = fs.save('logs/temp.kml', f)
            temp_file_path = fs.path(temp_file_name)

            # Convert KML to GeoJSON
            raw_features = kml2geojson.main.convert(temp_file_name)
            # Create a list to store all features
            all_features = []

            def extract_features(item):
                """Recursively extract all features from any nested structure"""
                if isinstance(item, dict):
                    # If this is a Feature with geometry, add it
                    if item.get('type') == 'Feature' and item.get('geometry'):
                        clean_feature = {
                            'type': 'Feature',
                            'properties': {
                                'Name': item.get('properties', {}).get('name',
                                                                       item.get('properties', {}).get('Name',
                                                                                                      'Unnamed'))
                            },
                            'geometry': item['geometry']
                        }
                        all_features.append(clean_feature)

                    # Check all dictionary values for more features
                    for value in item.values():
                        if isinstance(value, (dict, list)):
                            extract_features(value)

                elif isinstance(item, list):
                    # Process each item in the list
                    for element in item:
                        extract_features(element)

            # Process all raw features
            extract_features(raw_features)

            # Create the final GeoJSON structure
            geojson_data = {
                'type': 'FeatureCollection',
                'features': all_features
            }

            # Debug print
            print(f"Processing complete. Found {len(all_features)} features")
            if all_features:
                print("First feature sample:")
                print(json.dumps(all_features[0], indent=2))

            # Make sure GEODATA_ROOT exists
            os.makedirs(settings.GEODATA_ROOT, exist_ok=True)

            # Save the GeoJSON output
            geojson_path = os.path.join(settings.GEODATA_ROOT, 'camden_fiber.geojson')

            with open(geojson_path, 'w') as geojson_file:
                json.dump(geojson_data, geojson_file, indent=2)

            print(f"GeoJSON saved with {len(all_features)} features")

            # Clean up the temporary file
            fs.delete(temp_file_name)

        case _:
                #no file found
                print('no file found')
    return

def geocodeLatLng(lat,lng):
    gmaps = googlemaps.Client(key=settings.GOOGLE_API_KEY)
    result = gmaps.reverse_geocode((lat, lng))
    return result

#dump vault database as cvs output
@login_required(login_url='fiberPlan:login')
def export_vaults(request):
    vaults = Vault.objects.all()
    response = HttpResponse(
        content_type="text/csv",
        headers={"Content-Disposition": 'attachment; filename="vaults.csv"'},
    )
    writer = csv.writer(response)
    writer.writerow(['id', 'name', 'lat', 'lng', 'address', 'is_mini', 'is_enc', 'notes'])
    for vault in vaults:
        writer.writerow([vault.id, vault.name, vault.lat, vault.lng, vault.address, vault.is_mini, vault.is_enc, vault.notes])
    return response

# CostQuest / FCC BDC component
@login_required(login_url='fiberPlan:login')
def costquest(request):
    cq = myCostQuest()
    costquest_data = cq.loadDataFromCSV('/tmp', 'FCC_Active_BSL_06302024_rel_5.csv')
    with open('camden_costquest.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(costquest_data)
    # cq.saveData(costquest_data,'fcc')
    availability_data = cq.loadDataFromCSV('/tmp', 'FBA-corrected_1h2024.csv', '37029', 'availability')
    costquest_available = []
    for available in availability_data:
        for costquest in costquest_data:
            if costquest[0] == available[0]:
                costquest_available.append(costquest)
    with open('camden_availability.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(costquest_available)
    # error_log = cq.saveData(costquest_data,'availability')
    costquest_data = cq.loadData()
    context = {'data': list(costquest_data)}
    return render(request, 'hotspot/costquest.html', context)

@login_required(login_url='fiberPlan:login')
@csrf_protect
def savevault(request):
    if request.method == 'POST':
        name = request.POST.get('name')
        notes = request.POST.get('notes')
        if not request.POST.get('is_enc'):
            enc = 0
        else:
            enc = 1
        if not request.POST.get('is_mini'):
            mini = 0
        else:
            mini = 1
        address = request.POST.get('address')
        lat = request.POST.get('lat')
        lng = request.POST.get('lng')
        data = (name, notes, enc, mini, address, lat, lng)
        new_vault = Vault.objects.create(
            name=data[0],
            notes=data[1],
            lat=data[5],
            lng=data[6],
            address=data[4],
            is_enc=data[2],
            is_mini=data[3],
        )
        new_vault.save()
    return redirect('fiberPlan:map')


@login_required(login_url='fiberPlan:login')
@csrf_protect
def updatevault(request):
    if request.method == 'POST':
        name = request.POST.get('name')
        id = request.POST.get('id')
        notes = request.POST.get('notes')
        if not request.POST.get('is_enc'):
            enc = 0
        else:
            enc = 1
        if not request.POST.get('is_mini'):
            mini = 0
        else:
            mini = 1
        address = request.POST.get('address')
        lat = request.POST.get('lat')
        lng = request.POST.get('lng')
        vault = Vault.objects.get(id=id)
        vault.name = name
        vault.notes = notes
        vault.lat = lat
        vault.lng = lng
        vault.address = address
        vault.is_enc = enc
        vault.is_mini = mini
        vault.save()
    return redirect('fiberPlan:map')

@login_required(login_url='fiberPlan:login')
def json_response(request):
    param = request.GET.get('param', 'empty')
    result = []
    match param:
        case 'updatevault':
            lat = request.GET.get('lat')
            lng = request.GET.get('lng')
            name = request.GET.get('name')
            id = request.GET.get('id')
            notes = request.GET.get('notes')
            address = request.GET.get('address')
            if request.GET.get('is_enc') == '':
                is_enc = 0
            else:
                is_enc = 1
            if request.GET.get('is_mini') == '':
                is_mini = 0
            else:
                is_mini = 1
            vault = Vault.objects.get(id=id)
            vault.name = name
            vault.notes = notes
            vault.lat = lat
            vault.lng = lng
            vault.address = address
            vault.is_enc = is_enc
            vault.is_mini = is_mini
            vault.save()
        case 'geocodeLatLng':
            gmaps = googlemaps.Client(key=settings.GOOGLE_API_KEY)
            latLng = {'lat': int(float(request.GET.get('lat'))), 'lng': int(float(request.GET.get('lng')))}
            result = json.dumps(gmaps.reverse_geocode((request.GET.get('lat'), request.GET.get('lng'))))
            result = json.loads(result)
        case 'set_permissions-dontrunagain':
            user = User.objects.get(username='ronald')
            user.user_permissions.add(Permission.objects.get(codename='is_admin'))
            user.save()
            user = User.objects.get(username='shannon')
            user.user_permissions.add(Permission.objects.get(codename='is_staff'))
            user.save()
            user = User.objects.get(username='hd365')
            user.user_permissions.add(Permission.objects.get(codename='is_helpdesk'))
            user.save()
        case 'add_permissions-dontrunagain':
            content_type = ContentType.objects.get_for_model(Vault)
            permission = Permission.objects.create(
                codename='is_staff',
                name='Is Staff Member',
                content_type=content_type, )
            permission.save()
            permission = Permission.objects.create(
                codename='is_helpdesk',
                name='Is HD365 Member',
                content_type=content_type, )
            permission.save()
            permission = Permission.objects.create(
                codename='is_admin',
                name='Is Administrator',
                content_type=content_type, )
            permission.save()
        case 'list_permissions':
            permissions = Permission.objects.all()
            for permission in permissions:
                result.append({
                    'name': permission.name,
                    'codename': permission.codename})
        case 'add_user':
            username = request.GET.get('username')
            password = request.GET.get('password')
            user = User.objects.create_user(username, password)
            user.save()
        case 'delete_user':
            username = request.GET.get('username')
            user = User.objects.get(username=username)
            user.delete()
            user.save()
            result = 'User: ' + username + ' deleted'
        case 'change_password':
            username = request.GET.get('username')
            old_password = request.GET.get('old_password')
            password = request.GET.get('password')
            user = authenticate(username=username, password=old_password)
            if user is not None:
                user = User.objects.get(username=username)
                user.set_password(password)
                user.save()
                result = 'password changed'
            else:
                result = 'invalid credentials, password not changed'
        case 'logout_user':
            username = request.GET.get('username')
            user = User.objects.get(username=username)
        case 'list_users':
            users = User.objects.all()
            for user in users:
                result.append({'username': user.username, 'pass:': user.password})
        case 'get_all_vaults':
            result = list(Vault.objects.all() \
                          .values('id', 'name', 'lat', 'lng', 'address', 'is_mini', 'is_enc', 'notes'))
            for vault in result:
                vault['name'] = '<a href="vault/' + str(vault['id']) + '" target="_self">' + str(vault['name']) + '</a>'
        case 'get_map_data':
            result = list(Vault.objects \
                          .values('id', 'name', 'lat', 'lng', 'address', 'is_mini', 'is_enc', 'notes'))
        case _:
            result = {'result': False}
    return JsonResponse(result, safe=False)


def ensure_description(json_data):
    # Load JSON if it's a string
    if isinstance(json_data, str):
        data = json.loads(json_data)
    else:
        data = json_data

    # Iterate through each record and ensure "description" exists
    for record in data:
        if "description" not in record:
            record["description"] = "No description available"

    return data


@login_required(login_url='fiberPlan:login')
def admin(request):
    context = {}
    return render(request, 'vault/vaults.html', context)


#
# Login / Logout logic
#
def user_login(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)
        if user is not None:
            context = {'message': 'Login Succeeded.'}
            logger.info(
                'AUTH: User ' + str(username) + ' logged in on ' + datetime.now().strftime("%I:%M%p on %B %d, %Y"))
            login(request, user)
            return redirect('englishTowers:main')
        else:
            context = {'message': 'Login Failed, please try again.'}
            logger.info(
                'AUTH: User ' + str(username) + ' failed login on ' + datetime.now().strftime("%I:%M%p on %B %d, %Y"))
            return render(request, 'static/html/login.html', context)
    else:
        context = {'message': 'Please Login'}
        return render(request, 'static/html/login.html', context)


def user_logout(request):
    logout(request)


#
# vaults/map section of fiberPlan views
#
@login_required(login_url='fiberPlan:login')
def vaults(request):
    context = {}
    return render(request, 'vault/vaults.html', context)


@login_required(login_url='fiberPlan:login')
def vault(request, id):
    result_list = list(Vault.objects.filter(id=int(id)) \
                       .values('id', 'name', 'lat', 'lng', 'address', 'is_mini', 'is_enc', 'notes'))
    context = {
        'vaults': result_list
    }
    return render(request, "vault/vault.html", context)


@login_required(login_url='fiberPlan:login')
def remove_doubles(result_list):
    vault_list = result_list
    clean_list = result_list
    for vault in vault_list:
        search_id = vault['id']
        name = vault['name']
        lat = vault['lat']
        lng = vault['lng']
        for search_vault in result_list:
            if search_vault['name'] == name and search_vault['lat'] == lat and search_vault['lng'] == lng and \
                    search_vault['id'] != search_id:
                vault_obj = Vault.objects.get(id=search_vault['id'])
                vault_obj.delete()
                clean_list.remove(search_vault)
    return clean_list


@login_required(login_url='fiberPlan:login')
def geocode(request):
    gmaps = googlemaps.Client(key=settings.GOOGLE_API_KEY)
    result = json.dumps(gmaps.geocode(str('7978 Lexington Club Blvd, Delray Beach, FL, 33446')))
    result2 = json.loads(result)
    addressComponents = result2[0]['geometry']
    latitude = result2[0]['geometry']['location']['lat']
    longitude = result2[0]['geometry']['location']['lng']
    context = {
        'result': result,
        'latitude': latitude,
        'longitude': longitude
    }
    return render(request, "google/geocode.html", context)


@login_required(login_url='fiberPlan:login')
def distance(request):
    gmaps = googlemaps.Client(key=settings.GOOGLE_API_KEY)
    # driving, walking, bicycling, transit are valid modes
    result = json.dumps(gmaps.distance_matrix(
        '40 Rader Street, Norfolk',
        '7978 Lexington Club Blvd, Delray',
        mode='driving',
        departure_time=datetime.now()))
    result = json.loads(result)
    start = result['origin_addresses'][0]
    finish = result['destination_addresses'][0]
    distance = result['rows'][0]['elements'][0]['distance']['text']
    duration = result['rows'][0]['elements'][0]['duration']['text']
    context = {
        'start': start,
        'finish': finish,
        'distance': distance,
        'duration': duration,
    }
    return render(request, 'google/distance.html', context)


@login_required(login_url='fiberPlan:login')
def map(request):
    key = settings.GOOGLE_API_KEY
    context = {
        'key': key,
    }
    return render(request, 'google/map.html', context)


# AJAX Call to get all Vaults
@login_required(login_url='fiberPlan:login')
def mydata(request):
    result_list = {}
    result_list['customer'] = list(Customer.objects \
                       .values('clientId', 'lastName', 'firstName','lat', 'lng', 'street1', 'city', 'isCustomer'))
    result_list['vaults'] = list(Vault.objects \
                       .values('id', 'name', 'lat', 'lng', 'address', 'is_mini', 'is_enc', 'notes'))
    return JsonResponse(result_list, safe=False)




@csrf_protect
def signup(request):
    # Token should be passed as ?token=abcdef in URL
    token_value = request.GET.get('token', '')
    try:
        signup_token = SignupToken.objects.get(token=token_value)
    except SignupToken.DoesNotExist:
        raise Http404("Invalid or expired signup link.")
    if not signup_token.is_valid():
        return HttpResponseForbidden("Signup link expired or already used.")

    customer = signup_token.customer

    if request.method == "POST":
        # Parse form fields
        plan = request.POST.get('plan_selected')
        install_fee = request.POST.get('install_fee')
        network_fee = request.POST.get('network_maintenance_fee')
        modem_fee = request.POST.get('modem_lease_fee')
        router_fee = request.POST.get('router_lease_fee')
        construction = request.POST.get('additional_construction_costs')
        preferred_date = request.POST.get('preferred_installation_date')
        agrees_to_terms = request.POST.get('agrees_to_terms') == 'on'
        signature = request.POST.get('signature')
        signed_date = timezone.now().date()

        # Validate and save
        if not (plan and agrees_to_terms and signature):
            messages.error(request, "All required fields and agreements must be completed.")
        else:
            FiberSignupForm.objects.create(
                customer=customer,
                plan_selected=plan,
                install_fee=install_fee or None,
                network_maintenance_fee=network_fee or None,
                modem_lease_fee=modem_fee or None,
                router_lease_fee=router_fee or None,
                additional_construction_costs=construction or None,
                preferred_installation_date=preferred_date or None,
                agrees_to_terms=agrees_to_terms,
                signature=signature,
                signed_date=signed_date
            )
            signup_token.used = True
            signup_token.save()
            # Trigger notifications/audit here if desired
            messages.success(request, "Signup complete! Thank you.")
            return render(request, "fiberPlan/signup_success.html")

    # Render the actual form
    return render(request, "fiberPlan/signup.html", {
        "customer": customer,
        "token": token_value,
    })
