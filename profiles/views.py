from django.contrib.auth.views import PasswordChangeView
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.db.models.aggregates import Max
from django.urls import reverse_lazy
from .models import Profile
from .forms import ProfileForm
from auditlog.signals import audit_log_signal
from django.contrib.auth.decorators import user_passes_test
from django.contrib.auth.models import Group
from django.shortcuts import render, redirect, get_object_or_404
from .forms import GroupForm
from .forms import UserGroupPermissionForm
from auditlog.models import AuditLog
from django.http import JsonResponse
from django.contrib import messages
from fiberPlan.models import Customer, SignupToken
import secrets
from datetime import timedelta
from django.utils import timezone
import googlemaps
from django.conf import settings
import json


@login_required
def add_lead(request):
    if request.method == 'POST':
        # Gather form data
        first = request.POST.get('firstName')
        last  = request.POST.get('lastName')
        email = request.POST.get('email')
        street1 = request.POST.get('street1', '')
        city    = request.POST.get('city', '')
        # Latitude/longitude are optional
        lat = request.POST.get('lat') or None
        lng = request.POST.get('lng') or None

        # Compute a new unique clientId
        max_id = Customer.objects.aggregate(Max('clientId'))['clientId__max'] or 0
        client_id = max_id + 1

        # Create the lead
        Customer.objects.create(
            clientId=client_id,
            firstName=first,
            lastName=last,
            email=email,
            street1=street1,
            city=city,
            lat=lat,
            lng=lng,
            isLead=True,
            isCustomer=False
        )
        messages.success(request, "New lead added.")
    return redirect('lead_management')


@login_required
@user_passes_test(lambda u: u.is_superuser)
def delete_user(request):
    users = User.objects.all()
    if request.method == 'POST':
        user_id = request.POST.get('user_id')
        if user_id:
            try:
                user = User.objects.get(id=user_id)
                username = user.username
                user.delete()

                # Log creation event
                audit_log_signal.send(
                    sender=User,
                    request=request,
                    model_name='User',
                    object_id=user.id,
                    action='Delete',
                    extra_data={"Deleted User": username},
                )
                messages.success(request, f"User '{username}' deleted successfully.")
                return redirect('group_list')
            except User.DoesNotExist:
                messages.error(request, "Selected user does not exist.")
            except Exception as e:
                messages.error(request, f"Error deleting user: {str(e)}")
        else:
            messages.error(request, "Please select a user to delete.")
    return render(request, 'profiles/delete_user.html', {'users': users})


@login_required
@user_passes_test(lambda u: u.is_superuser)
def add_user(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        email = request.POST.get('email')
        password = request.POST.get('password')
        if username and email and password:
            try:
                user = User.objects.create_user(username=username, email=email, password=password)
                user.save()
                # Log creation event
                audit_log_signal.send(
                    sender=User,
                    request=request,
                    model_name='User',
                    object_id=user.id,
                    action='CREATE',
                    extra_data={'username': username, 'email': email},
                )

                messages.success(request, f"User '{username}' added successfully.")
                return redirect('group_list')  # or wherever appropriate
            except Exception as e:
                messages.error(request, f"Error adding user: {str(e)}")
        else:
            messages.error(request, "All fields are required.")
    return render(request, 'profiles/add_user.html')


@login_required
@user_passes_test(lambda u: u.is_superuser)
def change_user_password(request):
    users = User.objects.all()
    if request.method == 'POST':
        user_id = request.POST.get('user_id')
        new_password = request.POST.get('new_password')
        if user_id and new_password:
            try:
                user = User.objects.get(id=user_id)
                user.set_password(new_password)
                user.save()
                messages.success(request, f"Password for user '{user.username}' has been updated.")
                return redirect('group_list')
            except User.DoesNotExist:
                messages.error(request, "Selected user does not exist.")
            except Exception as e:
                messages.error(request, f"Error updating password: {str(e)}")
        else:
            messages.error(request, "Please select a user and provide a new password.")
    return render(request, 'profiles/change_user_password.html', {'users': users})


@login_required
def profile_view(request):
    # Check if profile exists, create if not
    profile, created = Profile.objects.get_or_create(user=request.user)

    # Optional: Redirect to edit page if newly created (no info yet)
    if created:
        return redirect('profile_edit')

    return render(request, 'profiles/profile_view.html', {'profile': profile})


@login_required
def profile_edit(request):
    # Check if profile exists, create if not
    profile, created = Profile.objects.get_or_create(user=request.user)

    # For audit logging
    original_data = {field: getattr(profile, field) for field in ProfileForm.Meta.fields}
    print(f"original_data: {original_data}")

    if request.method == 'POST':
        form = ProfileForm(request.POST, instance=profile)
        if form.is_valid():
            form.save()

            # Capture updated data after saving
            updated_data = {field: getattr(profile, field) for field in ProfileForm.Meta.fields}
            print(f"updated_data: {updated_data}")

            # Compare and log changes
            changes = {
                field: {"old": original_data[field], "new": updated_data[field]}
                for field in original_data
                if original_data[field] != updated_data[field]
            }

            audit_log_signal.send(
                sender=Profile,
                request=request,
                model_name='Profile',
                object_id=profile.id,
                action='CREATE' if created else 'UPDATE',
                extra_data=changes
            )

            return redirect('profile_view')
    else:
        form = ProfileForm(instance=profile)

    return render(request, 'profiles/edit_profile.html', {'form': form})


def superuser_required(view_func):
    return user_passes_test(lambda u: u.is_superuser)(view_func)


@superuser_required
def get_audits(request):
    return render(request, 'profiles/audit_view.html')


@superuser_required
def group_list(request):
    groups = Group.objects.all()
    return render(request, 'profiles/group_list.html', {'groups': groups})


@superuser_required
def group_create(request):
    if request.method == 'POST':
        form = GroupForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect('group_list')
    else:
        form = GroupForm()
    return render(request, 'profiles/group_form.html', {'form': form})


@superuser_required
def group_edit(request, group_id):
    group = Group.objects.get(id=group_id)
    if request.method == 'POST':
        form = GroupForm(request.POST, instance=group)
        if form.is_valid():
            form.save()
            return redirect('group_list')
    else:
        form = GroupForm(instance=group)
    return render(request, 'profiles/group_form.html', {'form': form, 'group': group})


@superuser_required
def group_delete(request, group_id):
    group = Group.objects.get(id=group_id)
    if request.method == 'POST':
        group.delete()
        return redirect('group_list')
    return render(request, 'profiles/group_confirm_delete.html', {'group': group})


@superuser_required
def manage_user_groups_permissions(request):
    users = User.objects.all()  # ✅ GET all users

    selected_user = None
    form = None

    if request.method == 'POST':
        user_id = request.POST.get('user_id')
        if user_id:
            selected_user = User.objects.get(id=user_id)
            form = UserGroupPermissionForm(request.POST, instance=selected_user)
            if form.is_valid():
                form.save()
    elif request.GET.get('user_id'):
        # If user selected via GET dropdown
        user_id = request.GET.get('user_id')
        selected_user = User.objects.get(id=user_id)
        form = UserGroupPermissionForm(instance=selected_user)

    # ✅ Make sure you are passing 'users' to template!
    return render(request, 'profiles/manage_user_groups.html', {
        'users': users,
        'selected_user': selected_user,
        'form': form
    })


@superuser_required
def audit_json(request):
    # Get audits and convert to a list of dictionaries
    audits = list(
        AuditLog.objects.all().order_by("-timestamp").values(
            'timestamp', 'user__username', 'model_name', 'object_id', 'action', 'extra_data'
        )
    )
    return JsonResponse(audits, safe=False)


class CustomPasswordChangeView(PasswordChangeView):
    template_name = 'profiles/password_change.html'
    success_url = reverse_lazy('password_change_done')

    def form_valid(self, form):
        response = super().form_valid(form)

        # Capture old password hash (or masked value) for audit trail
        old_password_hash = self.request.user.password
        form.save()  # Save new password

        # Capture updated password hash (or masked value)
        updated_password_hash = self.request.user.password

        # Trigger audit log signal with extra_data
        audit_log_signal.send(
            sender=self.request.user.__class__,
            request=self.request,
            model_name='User',
            object_id=self.request.user.id,
            action='PASSWORD_CHANGE',
            extra_data={
                "password": {
                    "old": f"Hash({old_password_hash[:8]}...)",  # Masked for security
                    "new": f"Hash({updated_password_hash[:8]}...)"  # Masked for security
                }
            }
        )

        return response

@login_required
def lead_management(request):
    leads = Customer.objects.filter(isLead=True, isCustomer=False)
    return render(request, 'profiles/lead_management.html', {
        'leads': leads
    })

@login_required
def delete_lead(request, id):
    lead = get_object_or_404(Customer, id=id, isLead=True, isCustomer=False)
    lead.delete()
    messages.success(request, 'Lead deleted.')
    return redirect('lead_management')

@login_required
def edit_lead(request, id):
    lead = get_object_or_404(Customer, id=id, isLead=True, isCustomer=False)
    if request.method == 'POST':
        lead.firstName = request.POST.get('firstName')
        lead.lastName  = request.POST.get('lastName')
        lead.email     = request.POST.get('email')
        lead.save()
        messages.success(request, 'Lead updated.')
        return redirect('lead_management')
    return render(request, 'profiles/edit_lead.html', {
        'lead': lead
    })


@login_required
def process_signup(request, id):
    """Convert a lead to a customer and create a signup token"""
    lead = get_object_or_404(Customer, id=id, isLead=True, isCustomer=False)

    # Convert lead to customer
    lead.isLead = False
    lead.isCustomer = True
    lead.save()

    # Create signup token
    token = create_signup_token(lead)

    messages.success(request, f'Lead processed successfully. Signup token: {token}')
    return redirect('lead_management')


@login_required
def geocode_address(request):
    """API endpoint to geocode an address and return lat/lng coordinates"""
    if request.method == 'GET':
        address = request.GET.get('address', '')
        if not address:
            return JsonResponse({'error': 'No address provided'}, status=400)

        try:
            gmaps = googlemaps.Client(key=settings.GOOGLE_API_KEY)
            result = gmaps.geocode(address)

            if result:
                location = result[0]['geometry']['location']
                return JsonResponse({
                    'success': True,
                    'lat': location['lat'],
                    'lng': location['lng'],
                    'formatted_address': result[0]['formatted_address']
                })
            else:
                return JsonResponse({'error': 'Address not found'}, status=404)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Invalid request method'}, status=405)


# Token generation function (call this in your admin workflow)
def create_signup_token(customer):
    token = secrets.token_urlsafe(32)
    expires = timezone.now() + timedelta(days=5)
    st = SignupToken.objects.create(customer=customer, token=token, expires_at=expires)
    return st.token